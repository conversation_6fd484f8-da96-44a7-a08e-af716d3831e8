["\\\\?\\D:\\github\\projects\\Peach Blossom Paper\\src-tauri\\target\\debug\\build\\tauri-f6a221909e277fe2\\out\\permissions\\path\\autogenerated\\commands\\basename.toml", "\\\\?\\D:\\github\\projects\\Peach Blossom Paper\\src-tauri\\target\\debug\\build\\tauri-f6a221909e277fe2\\out\\permissions\\path\\autogenerated\\commands\\dirname.toml", "\\\\?\\D:\\github\\projects\\Peach Blossom Paper\\src-tauri\\target\\debug\\build\\tauri-f6a221909e277fe2\\out\\permissions\\path\\autogenerated\\commands\\extname.toml", "\\\\?\\D:\\github\\projects\\Peach Blossom Paper\\src-tauri\\target\\debug\\build\\tauri-f6a221909e277fe2\\out\\permissions\\path\\autogenerated\\commands\\is_absolute.toml", "\\\\?\\D:\\github\\projects\\Peach Blossom Paper\\src-tauri\\target\\debug\\build\\tauri-f6a221909e277fe2\\out\\permissions\\path\\autogenerated\\commands\\join.toml", "\\\\?\\D:\\github\\projects\\Peach Blossom Paper\\src-tauri\\target\\debug\\build\\tauri-f6a221909e277fe2\\out\\permissions\\path\\autogenerated\\commands\\normalize.toml", "\\\\?\\D:\\github\\projects\\Peach Blossom Paper\\src-tauri\\target\\debug\\build\\tauri-f6a221909e277fe2\\out\\permissions\\path\\autogenerated\\commands\\resolve.toml", "\\\\?\\D:\\github\\projects\\Peach Blossom Paper\\src-tauri\\target\\debug\\build\\tauri-f6a221909e277fe2\\out\\permissions\\path\\autogenerated\\commands\\resolve_directory.toml", "\\\\?\\D:\\github\\projects\\Peach Blossom Paper\\src-tauri\\target\\debug\\build\\tauri-f6a221909e277fe2\\out\\permissions\\path\\autogenerated\\default.toml"]