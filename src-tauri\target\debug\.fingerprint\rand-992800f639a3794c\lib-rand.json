{"rustc": 1842507548689473721, "features": "[\"alloc\", \"default\", \"getrandom\", \"libc\", \"rand_chacha\", \"small_rng\", \"std\", \"std_rng\"]", "declared_features": "[\"alloc\", \"default\", \"getrandom\", \"libc\", \"log\", \"min_const_gen\", \"nightly\", \"packed_simd\", \"rand_chacha\", \"serde\", \"serde1\", \"simd_support\", \"small_rng\", \"std\", \"std_rng\"]", "target": 8827111241893198906, "profile": 2225463790103693989, "path": 3933751852742756838, "deps": [[1573238666360410412, "rand_chacha", false, 7565552738059081553], [18130209639506977569, "rand_core", false, 12545041465799034494]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\rand-992800f639a3794c\\dep-lib-rand", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}