# Copyright 2019-2024 Tauri Programme within The Commons Conservancy
# SPDX-License-Identifier: Apache-2.0
# SPDX-License-Identifier: MIT
# Automatically generated - DO NOT EDIT!

[[permission]]
identifier = "allow-set-maximizable"
description = "Enables the set_maximizable command without any pre-configured scope."
commands.allow = ["set_maximizable"]

[[permission]]
identifier = "deny-set-maximizable"
description = "Denies the set_maximizable command without any pre-configured scope."
commands.deny = ["set_maximizable"]
