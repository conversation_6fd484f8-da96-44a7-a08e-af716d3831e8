D:\github\projects\Peach Blossom Paper\src-tauri\target\debug\deps\markup5ever-8e2e3b4e4d8e1196.d: C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\lib.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\data\mod.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\interface\mod.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\interface\tree_builder.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\serialize.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\util\buffer_queue.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\util\smallcharset.rs D:\github\projects\Peach\ Blossom\ Paper\src-tauri\target\debug\build\markup5ever-983c74bbb9a4b1b8\out/generated.rs D:\github\projects\Peach\ Blossom\ Paper\src-tauri\target\debug\build\markup5ever-983c74bbb9a4b1b8\out/named_entities.rs

D:\github\projects\Peach Blossom Paper\src-tauri\target\debug\deps\libmarkup5ever-8e2e3b4e4d8e1196.rlib: C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\lib.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\data\mod.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\interface\mod.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\interface\tree_builder.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\serialize.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\util\buffer_queue.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\util\smallcharset.rs D:\github\projects\Peach\ Blossom\ Paper\src-tauri\target\debug\build\markup5ever-983c74bbb9a4b1b8\out/generated.rs D:\github\projects\Peach\ Blossom\ Paper\src-tauri\target\debug\build\markup5ever-983c74bbb9a4b1b8\out/named_entities.rs

D:\github\projects\Peach Blossom Paper\src-tauri\target\debug\deps\libmarkup5ever-8e2e3b4e4d8e1196.rmeta: C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\lib.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\data\mod.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\interface\mod.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\interface\tree_builder.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\serialize.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\util\buffer_queue.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\util\smallcharset.rs D:\github\projects\Peach\ Blossom\ Paper\src-tauri\target\debug\build\markup5ever-983c74bbb9a4b1b8\out/generated.rs D:\github\projects\Peach\ Blossom\ Paper\src-tauri\target\debug\build\markup5ever-983c74bbb9a4b1b8\out/named_entities.rs

C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\lib.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\data\mod.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\interface\mod.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\interface\tree_builder.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\serialize.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\util\buffer_queue.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\markup5ever-0.14.1\util\smallcharset.rs:
D:\github\projects\Peach\ Blossom\ Paper\src-tauri\target\debug\build\markup5ever-983c74bbb9a4b1b8\out/generated.rs:
D:\github\projects\Peach\ Blossom\ Paper\src-tauri\target\debug\build\markup5ever-983c74bbb9a4b1b8\out/named_entities.rs:

# env-dep:OUT_DIR=D:\\github\\projects\\Peach Blossom Paper\\src-tauri\\target\\debug\\build\\markup5ever-983c74bbb9a4b1b8\\out
