{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[]", "target": 13151518555585256095, "profile": 2241668132362809309, "path": 5081336466920372136, "deps": [[1462335029370885857, "quick_xml", false, 3372865369065720650], [3334271191048661305, "windows_version", false, 3649152975648343805], [12901820725121660946, "thiserror", false, 11115667579217614710], [14585479307175734061, "windows", false, 1599897099145170753]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-winrt-notification-bcf26d39f9764091\\dep-lib-tauri_winrt_notification", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}