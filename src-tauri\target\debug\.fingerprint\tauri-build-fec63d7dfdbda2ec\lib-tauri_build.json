{"rustc": 1842507548689473721, "features": "[\"config-json\", \"default\"]", "declared_features": "[\"codegen\", \"config-json\", \"config-json5\", \"config-toml\", \"default\", \"isolation\", \"quote\", \"tauri-codegen\"]", "target": 1006236803848883740, "profile": 2225463790103693989, "path": 19569072800467306, "deps": [[654232091421095663, "tauri_utils", false, 411915719033257560], [4824857623768494398, "cargo_toml", false, 14322995020477489745], [4899080583175475170, "semver", false, 512812654643943357], [5165059047667588304, "tauri_winres", false, 202582026491263167], [6913375703034175521, "schemars", false, 11296412438476476254], [7170110829644101142, "json_patch", false, 234274438235283131], [9293239362693504808, "glob", false, 15953679550155227386], [9689903380558560274, "serde", false, 1719330234165827094], [11207653606310558077, "anyhow", false, 12321044309432778963], [13077543566650298139, "heck", false, 2310260232102032935], [15609422047640926750, "toml", false, 53178940480223556], [15622660310229662834, "walkdir", false, 13141832595847327072], [16362055519698394275, "serde_json", false, 5251156310077764947], [16928111194414003569, "dirs", false, 9629153185681952333]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-build-fec63d7dfdbda2ec\\dep-lib-tauri_build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}