{"rustc": 1842507548689473721, "features": "[\"compression\"]", "declared_features": "[\"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"isolation\", \"tracing\"]", "target": 4649449654257170297, "profile": 2225463790103693989, "path": 4801497374888428737, "deps": [[48866495107080032, "syn", false, 17841074258507933517], [654232091421095663, "tauri_utils", false, 411915719033257560], [2704937418414716471, "tauri_codegen", false, 11708076097193113817], [13077543566650298139, "heck", false, 2310260232102032935], [13790829364578928694, "proc_macro2", false, 4746373800654736223], [17990358020177143287, "quote", false, 4241350359151612717]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-macros-5a1a3756b74eaffd\\dep-lib-tauri_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}