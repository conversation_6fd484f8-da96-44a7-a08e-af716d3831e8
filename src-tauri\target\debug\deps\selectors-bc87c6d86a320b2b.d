D:\github\projects\Peach Blossom Paper\src-tauri\target\debug\deps\selectors-bc87c6d86a320b2b.d: C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\selectors-0.24.0\lib.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\selectors-0.24.0\attr.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\selectors-0.24.0\bloom.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\selectors-0.24.0\builder.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\selectors-0.24.0\context.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\selectors-0.24.0\matching.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\selectors-0.24.0\nth_index_cache.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\selectors-0.24.0\parser.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\selectors-0.24.0\sink.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\selectors-0.24.0\tree.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\selectors-0.24.0\visitor.rs D:\github\projects\Peach\ Blossom\ Paper\src-tauri\target\debug\build\selectors-bf49c73f6558a541\out/ascii_case_insensitive_html_attributes.rs

D:\github\projects\Peach Blossom Paper\src-tauri\target\debug\deps\libselectors-bc87c6d86a320b2b.rlib: C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\selectors-0.24.0\lib.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\selectors-0.24.0\attr.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\selectors-0.24.0\bloom.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\selectors-0.24.0\builder.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\selectors-0.24.0\context.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\selectors-0.24.0\matching.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\selectors-0.24.0\nth_index_cache.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\selectors-0.24.0\parser.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\selectors-0.24.0\sink.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\selectors-0.24.0\tree.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\selectors-0.24.0\visitor.rs D:\github\projects\Peach\ Blossom\ Paper\src-tauri\target\debug\build\selectors-bf49c73f6558a541\out/ascii_case_insensitive_html_attributes.rs

D:\github\projects\Peach Blossom Paper\src-tauri\target\debug\deps\libselectors-bc87c6d86a320b2b.rmeta: C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\selectors-0.24.0\lib.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\selectors-0.24.0\attr.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\selectors-0.24.0\bloom.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\selectors-0.24.0\builder.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\selectors-0.24.0\context.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\selectors-0.24.0\matching.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\selectors-0.24.0\nth_index_cache.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\selectors-0.24.0\parser.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\selectors-0.24.0\sink.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\selectors-0.24.0\tree.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\selectors-0.24.0\visitor.rs D:\github\projects\Peach\ Blossom\ Paper\src-tauri\target\debug\build\selectors-bf49c73f6558a541\out/ascii_case_insensitive_html_attributes.rs

C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\selectors-0.24.0\lib.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\selectors-0.24.0\attr.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\selectors-0.24.0\bloom.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\selectors-0.24.0\builder.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\selectors-0.24.0\context.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\selectors-0.24.0\matching.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\selectors-0.24.0\nth_index_cache.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\selectors-0.24.0\parser.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\selectors-0.24.0\sink.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\selectors-0.24.0\tree.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\selectors-0.24.0\visitor.rs:
D:\github\projects\Peach\ Blossom\ Paper\src-tauri\target\debug\build\selectors-bf49c73f6558a541\out/ascii_case_insensitive_html_attributes.rs:

# env-dep:OUT_DIR=D:\\github\\projects\\Peach Blossom Paper\\src-tauri\\target\\debug\\build\\selectors-bf49c73f6558a541\\out
