{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[\"win7-notifications\", \"windows-version\", \"windows7-compat\"]", "target": 11906320761866078153, "profile": 2241668132362809309, "path": 16179478553355585338, "deps": [[947818755262499932, "notify_rust", false, 13787658408887624608], [3150220818285335163, "url", false, 11719759381509524158], [5986029879202738730, "log", false, 17103523495733528716], [7760050409050412348, "build_script_build", false, 16463502393432891272], [9689903380558560274, "serde", false, 6897187666579030334], [12092653563678505622, "tauri", false, 17258787798186913576], [12409575957772518135, "time", false, 8919569659935707982], [12901820725121660946, "thiserror", false, 11115667579217614710], [12986574360607194341, "serde_repr", false, 1914020974504138026], [13208667028893622512, "rand", false, 12291041101604136669], [16362055519698394275, "serde_json", false, 189229937043118516]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-plugin-notification-3e6d9464b4195f0d\\dep-lib-tauri_plugin_notification", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}