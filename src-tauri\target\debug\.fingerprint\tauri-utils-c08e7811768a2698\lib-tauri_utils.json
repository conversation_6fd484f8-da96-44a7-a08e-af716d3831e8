{"rustc": 1842507548689473721, "features": "[\"brotli\", \"compression\", \"resources\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"html-manipulation\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 2241668132362809309, "path": 83139694899628915, "deps": [[1200537532907108615, "url<PERSON><PERSON>n", false, 2423152280778918379], [2995469292676432503, "uuid", false, 16779476252504316075], [3150220818285335163, "url", false, 11719759381509524158], [4071963112282141418, "serde_with", false, 15867087818973498314], [4899080583175475170, "semver", false, 16197991607841655478], [5986029879202738730, "log", false, 17103523495733528716], [6606131838865521726, "ctor", false, 1066865794693811458], [7170110829644101142, "json_patch", false, 18247698911229135104], [9010263965687315507, "http", false, 17046921390355211077], [9293239362693504808, "glob", false, 7268526671042401294], [9451456094439810778, "regex", false, 8035670133280570470], [9556762810601084293, "brotli", false, 5646480653990378443], [9689903380558560274, "serde", false, 6897187666579030334], [11207653606310558077, "anyhow", false, 5190090078337922272], [11989259058781683633, "dunce", false, 11560442118751391268], [12901820725121660946, "thiserror", false, 11115667579217614710], [15609422047640926750, "toml", false, 13470608449098720666], [15622660310229662834, "walkdir", false, 11249427753159787704], [15932120279885307830, "memchr", false, 1811853779724236704], [16362055519698394275, "serde_json", false, 189229937043118516], [17146114186171651583, "infer", false, 10855312884651107782], [17183029615630212089, "serde_untagged", false, 3835732186369585259], [17186037756130803222, "phf", false, 1123292702654694940]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-utils-c08e7811768a2698\\dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}