{"rustc": 1842507548689473721, "features": "[\"build\"]", "declared_features": "[\"build\", \"runtime\"]", "target": 15996119522804316622, "profile": 2225463790103693989, "path": 773708378094716920, "deps": [[654232091421095663, "tauri_utils", false, 411915719033257560], [6913375703034175521, "schemars", false, 11296412438476476254], [9293239362693504808, "glob", false, 15953679550155227386], [9689903380558560274, "serde", false, 1719330234165827094], [11207653606310558077, "anyhow", false, 12321044309432778963], [15609422047640926750, "toml", false, 53178940480223556], [15622660310229662834, "walkdir", false, 13141832595847327072], [16362055519698394275, "serde_json", false, 5251156310077764947]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-plugin-30534a3f76b71841\\dep-lib-tauri_plugin", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}