{"rustc": 1842507548689473721, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[10885585936824290199, "build_script_build", false, 6077387101149625802], [12092653563678505622, "build_script_build", false, 7413305384623459272], [12504415026414629397, "build_script_build", false, 6678992611462309770], [16702348383442838006, "build_script_build", false, 10595390488086378782]], "local": [{"RerunIfChanged": {"output": "debug\\build\\peach-blossom-paper-b1d6c8dc1dab4efd\\output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}