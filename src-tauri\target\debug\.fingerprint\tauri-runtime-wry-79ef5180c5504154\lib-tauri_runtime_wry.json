{"rustc": 1842507548689473721, "features": "[\"common-controls-v6\", \"macos-private-api\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"default\", \"devtools\", \"macos-private-api\", \"macos-proxy\", \"objc-exception\", \"tracing\", \"unstable\", \"x11\"]", "target": 1901661049345253480, "profile": 2241668132362809309, "path": 7590839994313063594, "deps": [[376837177317575824, "softbuffer", false, 10804847024697533546], [654232091421095663, "tauri_utils", false, 5015333842816926953], [2013030631243296465, "webview2_com", false, 6830197056073221011], [3150220818285335163, "url", false, 11719759381509524158], [3722963349756955755, "once_cell", false, 9148332056717002387], [4143744114649553716, "raw_window_handle", false, 126447740291223225], [5986029879202738730, "log", false, 17103523495733528716], [8826339825490770380, "tao", false, 18160668603915002014], [9010263965687315507, "http", false, 17046921390355211077], [9141053277961803901, "wry", false, 5643046632600812166], [12304025191202589669, "build_script_build", false, 9419136493983249463], [12943761728066819757, "tauri_runtime", false, 3316108305245883935], [14585479307175734061, "windows", false, 1599897099145170753]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-wry-79ef5180c5504154\\dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}