# Copyright 2019-2024 Tauri Programme within The Commons Conservancy
# SPDX-License-Identifier: Apache-2.0
# SPDX-License-Identifier: MIT
# Automatically generated - DO NOT EDIT!

[[permission]]
identifier = "allow-reparent"
description = "Enables the reparent command without any pre-configured scope."
commands.allow = ["reparent"]

[[permission]]
identifier = "deny-reparent"
description = "Denies the reparent command without any pre-configured scope."
commands.deny = ["reparent"]
