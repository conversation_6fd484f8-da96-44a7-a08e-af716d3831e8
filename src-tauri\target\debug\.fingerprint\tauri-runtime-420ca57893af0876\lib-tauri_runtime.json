{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\"]", "target": 10306386172444932100, "profile": 2241668132362809309, "path": 48225779667954856, "deps": [[654232091421095663, "tauri_utils", false, 8829227379134465308], [3150220818285335163, "url", false, 11719759381509524158], [4143744114649553716, "raw_window_handle", false, 126447740291223225], [7606335748176206944, "dpi", false, 11423940567703900994], [9010263965687315507, "http", false, 17046921390355211077], [9689903380558560274, "serde", false, 6897187666579030334], [12901820725121660946, "thiserror", false, 11115667579217614710], [12943761728066819757, "build_script_build", false, 15488795224673834313], [14585479307175734061, "windows", false, 16381294448690139630], [16362055519698394275, "serde_json", false, 189229937043118516], [16727543399706004146, "cookie", false, 5768184357568065378]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-420ca57893af0876\\dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}